import type { Context } from "hono";
import { success, internalServerError } from "../utils/response.util";

// Mock service for now
const mockEmployees = [
  { id: 1, name: "<PERSON>", position: "<PERSON><PERSON><PERSON>" },
  { id: 2, name: "<PERSON>", position: "Designer" },
];

export const getAllEmployees = (c: Context) => {
  try {
    // Using mock data for now
    const employees = mockEmployees;
    return success(c, employees, {
      message: "Employees retrieved successfully",
    });
  } catch (error) {
    return internalServerError(c, "Failed to retrieve employees", {
      originalError: (error as Error).message,
    });
  }
};
