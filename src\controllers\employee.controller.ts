import type { Context } from "hono";
import { EmployeeService } from "../services/employeeService";
import { success, internalServerError } from "../utils/response.util";

const service = new EmployeeService();

export const getAllEmployees = (c: Context) => {
  try {
    const employees = service.getAllEmployees();
    return success(c, employees, {
      message: "Employees retrieved successfully",
    });
  } catch (error) {
    return internalServerError(c, "Failed to retrieve employees", {
      originalError: (error as Error).message,
    });
  }
};
