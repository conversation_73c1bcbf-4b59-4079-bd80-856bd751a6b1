import { Hono } from "hono";
import v1Routes from "./routes/v1.routes";
import { success } from "./utils/response.util";

const app = new Hono();
const startTime = Date.now();

app.get("/health-check", (c) => {
  const uptime = (Date.now() - startTime) / 1000;
  return success(
    c,
    {
      status: "OK",
      uptimeSeconds: uptime,
    },
    { message: "Service is healthy" }
  );
});

app.route("/api/v1", v1Routes);

export default app;
