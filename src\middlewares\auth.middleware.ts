import { Context, Next } from "hono";
import { getSupabaseClient } from "../lib/supabase";
import { unauthorized, internalServerError } from "../utils/response.util";
import type { User } from "@supabase/supabase-js";

export const authMiddleware = async (c: Context, next: Next) => {
  const authHeader = c.req.header("Authorization");

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return unauthorized(c, "Missing Bearer token");
  }

  const token = authHeader.replace("Bearer ", "").trim();
  const supabase = getSupabaseClient();

  try {
    const { data, error } = await supabase.auth.getUser(token);

    if (error || !data?.user) {
      return unauthorized(c, "Invalid token", {
        supabaseError: error?.message,
      });
    }

    c.set("user", data.user as User);

    await next();
  } catch (err: unknown) {
    return internalServerError(c, "Authentication service error", {
      originalError: (err as Error).message,
    });
  }
};
