import { Context, Next } from 'hono';
import { getSupabaseClient } from '../lib/supabase';
import type { User } from '@supabase/supabase-js';

export const authMiddleware = async (c: Context, next: Next) => {
  const authHeader = c.req.header('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return c.json({ error: 'Unauthorized - Missing Bearer token' }, 401);
  }

  const token = authHeader.replace('Bearer ', '').trim();
  const supabase = getSupabaseClient();

  try {
    const { data, error } = await supabase.auth.getUser(token);

    if (error || !data?.user) {
      return c.json({ error: 'Unauthorized - Invalid token' }, 401);
    }

    c.set('user', data.user as User);

    await next();
  } catch (err: unknown) {
    return c.json({ error: 'Unauthorized - ' + (err as Error).message }, 401);
  }
};
