# Response Utility Guide

This guide explains how to use the improved response utility functions in your Hono application.

## Overview

The response utility provides a consistent way to handle API responses with proper TypeScript typing, standardized error codes, and automatic timestamp inclusion.

## Features

- ✅ **Type Safety**: Full TypeScript support with proper return types
- ✅ **Consistent Structure**: All responses follow the same format
- ✅ **Automatic Timestamps**: Every response includes a timestamp
- ✅ **HTTP Status Constants**: Predefined status codes for better maintainability
- ✅ **Convenience Functions**: Common error scenarios have dedicated functions
- ✅ **Error Details**: Optional error details for debugging

## Response Structure

### Success Response
```typescript
{
  success: true,
  data: T,
  message?: string,
  timestamp: number
}
```

### Error Response
```typescript
{
  success: false,
  error: {
    code: string,
    message: string,
    details?: unknown
  },
  timestamp: number
}
```

## Basic Usage

### Success Responses

```typescript
import { success, created } from '../utils/response.util';

// Basic success response
export const getUser = (c: Context) => {
  const user = { id: 1, name: '<PERSON>' };
  return success(c, user);
};

// Success with custom message
export const getUsers = (c: Context) => {
  const users = [{ id: 1, name: '<PERSON>' }];
  return success(c, users, { message: 'Users retrieved successfully' });
};

// Created response (201 status)
export const createUser = (c: Context) => {
  const newUser = { id: 2, name: 'Jane Doe' };
  return created(c, newUser, 'User created successfully');
};
```

### Error Responses

```typescript
import { 
  error, 
  badRequest, 
  unauthorized, 
  notFound, 
  internalServerError 
} from '../utils/response.util';

// Generic error
export const someHandler = (c: Context) => {
  return error(c, 'CUSTOM_ERROR', 'Something went wrong', {
    status: 422,
    details: { field: 'validation failed' }
  });
};

// Convenience functions
export const validateInput = (c: Context) => {
  return badRequest(c, 'Invalid input data', { 
    field: 'email', 
    reason: 'Invalid format' 
  });
};

export const checkAuth = (c: Context) => {
  return unauthorized(c, 'Token expired');
};

export const findResource = (c: Context) => {
  return notFound(c, 'User not found');
};

export const handleError = (c: Context) => {
  return internalServerError(c, 'Database connection failed', {
    originalError: 'Connection timeout'
  });
};
```

## Available Functions

### Core Functions

- `success<T>(c: Context, data: T, options?: { message?: string, status?: number })`
- `error(c: Context, code: string, message: string, options?: { status?: number, details?: unknown })`

### Convenience Functions

- `badRequest(c: Context, message: string, details?: unknown)` - 400
- `unauthorized(c: Context, message?: string, details?: unknown)` - 401
- `forbidden(c: Context, message?: string, details?: unknown)` - 403
- `notFound(c: Context, message?: string, details?: unknown)` - 404
- `conflict(c: Context, message: string, details?: unknown)` - 409
- `unprocessableEntity(c: Context, message: string, details?: unknown)` - 422
- `internalServerError(c: Context, message?: string, details?: unknown)` - 500
- `created<T>(c: Context, data: T, message?: string)` - 201
- `noContent(c: Context)` - 204

## HTTP Status Constants

```typescript
import { HTTP_STATUS } from '../utils/response.util';

// Available constants:
HTTP_STATUS.OK                    // 200
HTTP_STATUS.CREATED               // 201
HTTP_STATUS.NO_CONTENT            // 204
HTTP_STATUS.BAD_REQUEST           // 400
HTTP_STATUS.UNAUTHORIZED          // 401
HTTP_STATUS.FORBIDDEN             // 403
HTTP_STATUS.NOT_FOUND             // 404
HTTP_STATUS.CONFLICT              // 409
HTTP_STATUS.UNPROCESSABLE_ENTITY  // 422
HTTP_STATUS.INTERNAL_SERVER_ERROR // 500
```

## Real-World Examples

### Employee Controller
```typescript
import { success, notFound, internalServerError } from '../utils/response.util';

export const getEmployee = async (c: Context) => {
  try {
    const id = c.req.param('id');
    const employee = await employeeService.findById(id);
    
    if (!employee) {
      return notFound(c, 'Employee not found');
    }
    
    return success(c, employee, { message: 'Employee retrieved successfully' });
  } catch (error) {
    return internalServerError(c, 'Failed to retrieve employee', {
      originalError: (error as Error).message
    });
  }
};
```

### Auth Middleware
```typescript
import { unauthorized, internalServerError } from '../utils/response.util';

export const authMiddleware = async (c: Context, next: Next) => {
  try {
    const token = c.req.header('Authorization');
    
    if (!token) {
      return unauthorized(c, 'Missing authorization token');
    }
    
    const user = await validateToken(token);
    if (!user) {
      return unauthorized(c, 'Invalid token');
    }
    
    c.set('user', user);
    await next();
  } catch (error) {
    return internalServerError(c, 'Authentication service error', {
      originalError: (error as Error).message
    });
  }
};
```

## Migration from Direct c.json() Calls

### Before
```typescript
// Old way
return c.json({ error: 'User not found' }, 404);
return c.json({ data: users }, 200);
```

### After
```typescript
// New way
return notFound(c, 'User not found');
return success(c, users);
```

## Benefits

1. **Consistency**: All responses follow the same structure
2. **Type Safety**: Full TypeScript support prevents runtime errors
3. **Maintainability**: Centralized response handling makes changes easier
4. **Debugging**: Automatic timestamps and optional error details
5. **Developer Experience**: Convenience functions reduce boilerplate code
6. **Standards Compliance**: Proper HTTP status codes and error formats

## Testing

The utility includes comprehensive tests. Run them with:

```bash
npm test src/utils/__tests__/response.util.test.ts
```

## Best Practices

1. Always use the convenience functions when available
2. Include meaningful error messages
3. Add error details for debugging in development
4. Use appropriate HTTP status codes
5. Keep error codes consistent across your application
6. Include success messages for important operations
