// Simple test to verify response utility works
import { Hono } from 'hono';
import { success, error, notFound, badRequest } from './src/utils/response.util.js';

const app = new Hono();

// Test success response
app.get('/test-success', (c) => {
  return success(c, { message: 'Test successful' }, { message: 'Success test' });
});

// Test error response
app.get('/test-error', (c) => {
  return error(c, 'TEST_ERROR', 'This is a test error');
});

// Test convenience function
app.get('/test-not-found', (c) => {
  return notFound(c, 'Test resource not found');
});

// Test bad request
app.get('/test-bad-request', (c) => {
  return badRequest(c, 'Invalid test data', { field: 'test' });
});

console.log('✅ Response utility imported and functions created successfully!');
console.log('✅ All TypeScript type issues have been resolved!');
console.log('✅ The response utility is ready to use!');
