import { Context } from 'hono';
import {
  success,
  error,
  badRequest,
  unauthorized,
  forbidden,
  notFound,
  conflict,
  unprocessableEntity,
  internalServerError,
  created,
  noContent,
  HTTP_STATUS,
  type SuccessResponse,
  type ErrorResponse,
} from '../response.util';

// Mock Hono Context
const createMockContext = () => {
  const mockResponse = { status: 200, body: null };
  return {
    json: jest.fn((body: any, status?: number) => {
      mockResponse.body = body;
      if (status) mockResponse.status = status;
      return mockResponse;
    }),
    body: jest.fn((body: any, status?: number) => {
      mockResponse.body = body;
      if (status) mockResponse.status = status;
      return mockResponse;
    }),
  } as unknown as Context;
};

describe('Response Utilities', () => {
  let mockContext: Context;

  beforeEach(() => {
    mockContext = createMockContext();
    jest.clearAllMocks();
  });

  describe('success', () => {
    it('should return a success response with default status', () => {
      const data = { id: 1, name: 'Test' };
      const result = success(mockContext, data);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data,
          timestamp: expect.any(Number),
        }),
        HTTP_STATUS.OK
      );
    });

    it('should return a success response with custom message and status', () => {
      const data = { id: 1, name: 'Test' };
      const message = 'Custom success message';
      const status = HTTP_STATUS.CREATED;

      success(mockContext, data, { message, status });

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data,
          message,
          timestamp: expect.any(Number),
        }),
        status
      );
    });
  });

  describe('error', () => {
    it('should return an error response with default status', () => {
      const code = 'TEST_ERROR';
      const message = 'Test error message';

      error(mockContext, code, message);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          timestamp: expect.any(Number),
          error: {
            code,
            message,
          },
        }),
        HTTP_STATUS.BAD_REQUEST
      );
    });

    it('should return an error response with details and custom status', () => {
      const code = 'TEST_ERROR';
      const message = 'Test error message';
      const details = { field: 'validation error' };
      const status = HTTP_STATUS.UNPROCESSABLE_ENTITY;

      error(mockContext, code, message, { status, details });

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          timestamp: expect.any(Number),
          error: {
            code,
            message,
            details,
          },
        }),
        status
      );
    });
  });

  describe('convenience functions', () => {
    it('should call badRequest with correct parameters', () => {
      const message = 'Bad request message';
      const details = { field: 'invalid' };

      badRequest(mockContext, message, details);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.objectContaining({
            code: 'BAD_REQUEST',
            message,
            details,
          }),
        }),
        HTTP_STATUS.BAD_REQUEST
      );
    });

    it('should call unauthorized with default message', () => {
      unauthorized(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.objectContaining({
            code: 'UNAUTHORIZED',
            message: 'Unauthorized',
          }),
        }),
        HTTP_STATUS.UNAUTHORIZED
      );
    });

    it('should call notFound with default message', () => {
      notFound(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.objectContaining({
            code: 'NOT_FOUND',
            message: 'Resource not found',
          }),
        }),
        HTTP_STATUS.NOT_FOUND
      );
    });

    it('should call created with success response', () => {
      const data = { id: 1, name: 'New Resource' };
      const message = 'Custom created message';

      created(mockContext, data, message);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          data,
          message,
        }),
        HTTP_STATUS.CREATED
      );
    });

    it('should call noContent with null body', () => {
      noContent(mockContext);

      expect(mockContext.body).toHaveBeenCalledWith(null, HTTP_STATUS.NO_CONTENT);
    });
  });

  describe('HTTP_STATUS constants', () => {
    it('should have correct status codes', () => {
      expect(HTTP_STATUS.OK).toBe(200);
      expect(HTTP_STATUS.CREATED).toBe(201);
      expect(HTTP_STATUS.NO_CONTENT).toBe(204);
      expect(HTTP_STATUS.BAD_REQUEST).toBe(400);
      expect(HTTP_STATUS.UNAUTHORIZED).toBe(401);
      expect(HTTP_STATUS.FORBIDDEN).toBe(403);
      expect(HTTP_STATUS.NOT_FOUND).toBe(404);
      expect(HTTP_STATUS.CONFLICT).toBe(409);
      expect(HTTP_STATUS.UNPROCESSABLE_ENTITY).toBe(422);
      expect(HTTP_STATUS.INTERNAL_SERVER_ERROR).toBe(500);
    });
  });
});
