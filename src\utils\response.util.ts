import { Context } from "hono";

// Base response interface
interface BaseResponse {
  success: boolean;
  timestamp: number;
}

// Success response type
export interface SuccessResponse<T = unknown> extends BaseResponse {
  success: true;
  data: T;
  message?: string;
}

// Error response type
export interface ErrorResponse extends BaseResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: unknown;
  };
}

// Union type for all possible responses
export type ApiResponse<T = unknown> = SuccessResponse<T> | ErrorResponse;

// Common HTTP status codes for better type safety
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Success response function with improved type safety
export function success<T>(
  c: Context,
  data: T,
  options?: {
    message?: string;
    status?: number;
  }
) {
  const { message, status = HTTP_STATUS.OK } = options || {};

  const body: SuccessResponse<T> = {
    success: true,
    data,
    timestamp: Date.now(),
  };

  if (message) {
    body.message = message;
  }

  return c.json(body, status);
}

// Error response function with improved type safety and common error codes
export function error(
  c: Context,
  code: string,
  message: string,
  options?: {
    status?: number;
    details?: unknown;
  }
) {
  const { status = HTTP_STATUS.BAD_REQUEST, details } = options || {};

  const body: ErrorResponse = {
    success: false,
    timestamp: Date.now(),
    error: {
      code,
      message,
    },
  };

  if (details !== undefined) {
    body.error.details = details;
  }

  return c.json(body, status);
}

// Convenience functions for common error scenarios
export function badRequest(c: Context, message: string, details?: unknown) {
  return error(c, "BAD_REQUEST", message, {
    status: HTTP_STATUS.BAD_REQUEST,
    details,
  });
}

export function unauthorized(
  c: Context,
  message: string = "Unauthorized",
  details?: unknown
) {
  return error(c, "UNAUTHORIZED", message, {
    status: HTTP_STATUS.UNAUTHORIZED,
    details,
  });
}

export function forbidden(
  c: Context,
  message: string = "Forbidden",
  details?: unknown
) {
  return error(c, "FORBIDDEN", message, {
    status: HTTP_STATUS.FORBIDDEN,
    details,
  });
}

export function notFound(
  c: Context,
  message: string = "Resource not found",
  details?: unknown
) {
  return error(c, "NOT_FOUND", message, {
    status: HTTP_STATUS.NOT_FOUND,
    details,
  });
}

export function conflict(c: Context, message: string, details?: unknown) {
  return error(c, "CONFLICT", message, {
    status: HTTP_STATUS.CONFLICT,
    details,
  });
}

export function unprocessableEntity(
  c: Context,
  message: string,
  details?: unknown
) {
  return error(c, "UNPROCESSABLE_ENTITY", message, {
    status: HTTP_STATUS.UNPROCESSABLE_ENTITY,
    details,
  });
}

export function internalServerError(
  c: Context,
  message: string = "Internal server error",
  details?: unknown
) {
  return error(c, "INTERNAL_SERVER_ERROR", message, {
    status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
    details,
  });
}

// Convenience function for created resources
export function created<T>(c: Context, data: T, message?: string) {
  return success(c, data, {
    message: message || "Resource created successfully",
    status: HTTP_STATUS.CREATED,
  });
}

// Convenience function for no content responses
export function noContent(c: Context) {
  return c.body(null, HTTP_STATUS.NO_CONTENT);
}
